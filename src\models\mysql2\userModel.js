import BaseModel from './BaseModel.js';
import { Enums, Fields, Queries, Utils } from './helpers.js';
import MySQL2Service from '../../services/mysql2Service.js';
import logger from '../../config/logger.js';

/**
 * Model User - <PERSON>u<PERSON>n lý thông tin người dùng
 */
class UserModel extends BaseModel {
  static get tableName() {
    return 'user';
  }

  constructor() {
    super();
    this.tableName = UserModel.tableName;
  }

  /**
   * Đ<PERSON>nh nghĩa các field của bảng user
   */
  static getFields() {
    return [
      ...super.getBaseFields(),
      'user_id',
      'user_name', 
      'email',
      'phone',
      'status',
      'ip',
      'create_time'
    ];
  }

  /**
   * Lấy enum status
   */
  static getStatusEnum() {
    return Enums.USER_STATUS;
  }

  /**
   * Transform dữ liệu user
   */
  transform(row) {
    if (!row) return null;
    
    return {
      ...row,
      ip: Utils.formatIP(row.ip),
      email: Utils.sanitizeString(row.email),
      phone: Utils.sanitizeString(row.phone),
      createTimeFormatted: row.create_time ? Utils.timestampToDate(row.create_time) : null,
    };
  }

  /**
   * Validate dữ liệu user
   */
  validate(data) {
    super.validate(data);
    
    if (data.email && !Utils.isValidEmail(data.email)) {
      throw new Error('Invalid email format');
    }
    
    if (data.phone && !Utils.isValidPhone(data.phone)) {
      throw new Error('Invalid phone format');
    }
    
    return true;
  }

  // === BASIC CRUD OPERATIONS ===

  /**
   * Đếm tổng số tài khoản (tương đương estimatedDocumentCount)
   */
  async estimatedDocumentCount() {
    try {
      return await this.countDocuments();
    } catch (error) {
      logger.error('UserModel.estimatedDocumentCount error:', error);
      throw error;
    }
  }

  /**
   * Đếm số tài khoản theo điều kiện
   * @param {Object} conditions - Điều kiện query từ helpers
   */
  async countDocuments(conditions = {}) {
    try {
      const { whereClause, params } = this.buildWhereClause(conditions);
      // Sử dụng service để thực hiện query
      return await MySQL2Service.count(this.tableName, whereClause, params);
    } catch (error) {
      logger.error('UserModel.countDocuments error:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách IP duy nhất (tương đương distinct)
   * @param {string} field - Field để lấy distinct
   * @param {Object} conditions - Điều kiện query từ helpers
   */
  async distinct(field, conditions = {}) {
    try {
      if (field === 'ip') {
        const { whereClause, params } = this.buildWhereClause(conditions);
        let sql = `SELECT DISTINCT ${field} FROM ${this.tableName}`;
        
        if (whereClause !== '1') {
          sql += ` WHERE ${whereClause}`;
        }
        
        // Thêm điều kiện để loại bỏ IP null hoặc rỗng
        if (whereClause === '1') {
          sql += ` WHERE ${field} IS NOT NULL AND ${field} != ''`;
        } else {
          sql += ` AND ${field} IS NOT NULL AND ${field} != ''`;
        }

        const rows = await MySQL2Service.query(sql, params);
        return rows.map(row => Utils.formatIP(row[field])).filter(ip => ip);
      }
      
      throw new Error(`Distinct field '${field}' is not supported`);
    } catch (error) {
      logger.error('UserModel.distinct error:', error);
      throw error;
    }
  }

  /**
   * Lấy thông tin user theo ID
   * @param {number} userId - ID của user
   */
  async findByUserId(userId) {
    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE user_id = ?`;
      const result = await MySQL2Service.getOne(sql, [userId]);
      return this.transform(result);
    } catch (error) {
      logger.error('UserModel.findByUserId error:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách user theo điều kiện
   * @param {Object} conditions - Điều kiện query từ helpers  
   * @param {Object} options - Tùy chọn (limit, offset, orderBy)
   */
  async find(conditions = {}, options = {}) {
    try {
      const { whereClause, params } = this.buildWhereClause(conditions);
      let sql = `SELECT * FROM ${this.tableName}`;
      
      if (whereClause !== '1') {
        sql += ` WHERE ${whereClause}`;
      }

      const orderByClause = this.buildOrderByClause(options.orderBy);
      if (orderByClause) {
        sql += ` ORDER BY ${orderByClause}`;
      }

      const limitClause = this.buildLimitClause(options.limit, options.offset);
      if (limitClause) {
        sql += limitClause;
      }

      const rows = await MySQL2Service.query(sql, params);
      return this.transformMany(rows);
    } catch (error) {
      logger.error('UserModel.find error:', error);
      throw error;
    }
  }

  // === QUERY HELPERS USING PATTERN ===

  /**
   * Tìm user active
   */
  async findActive(options = {}) {
    return await this.find(Queries.activeUsers().where, options);
  }

  /**
   * Tìm user theo IP
   */
  async findByIP(ip, options = {}) {
    return await this.find(Queries.byIP(ip).where, options);
  }

  /**
   * Tìm user trong khoảng thời gian
   */
  async findByTimeRange(startTime, endTime, options = {}) {
    return await this.find(Queries.byTimeRange(startTime, endTime).where, options);
  }

  /**
   * Tìm user hôm nay
   */
  async findToday(options = {}) {
    return await this.find(Queries.today().where, options);
  }

  /**
   * Tìm user tháng này
   */
  async findThisMonth(options = {}) {
    return await this.find(Queries.thisMonth().where, options);
  }

  /**
   * Tìm user với IP hợp lệ
   */
  async findWithValidIP(options = {}) {
    return await this.find(Queries.withValidIP().where, options);
  }

  /**
   * Đếm user active
   */
  async countActive() {
    return await this.countDocuments(Queries.activeUsers().where);
  }

  /**
   * Đếm user hôm nay
   */
  async countToday() {
    return await this.countDocuments(Queries.today().where);
  }

  /**
   * Đếm user tháng này
   */
  async countThisMonth() {
    return await this.countDocuments(Queries.thisMonth().where);
  }
}

// Export instance của class (singleton pattern)
export default new UserModel();
