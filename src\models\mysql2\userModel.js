import BaseModel from './BaseModel.js';
import { Enums, Fields, Utils } from './helpers.js';

/**
 * Model User - Qu<PERSON>n lý thông tin người dùng
 * Chỉ chứa định nghĩa cấu trúc, logic query được tách ra service
 */
export default class UserModel extends BaseModel {
  static get tableName() {
    return 'user';
  }

  /**
   * Định nghĩa các field của bảng user
   */
  static getFields() {
    return Object.assign({}, super.getBaseFields(), {
      user_id: Fields.userId(),
      user_name: Fields.userName(),
      email: Fields.email(),
      phone: Fields.phone(),
      status: Fields.status(),
      ip: Fields.ip(),
      create_time: Fields.createTime(),
    });
  }

  /**
   * C<PERSON>u hình tùy chọn
   */
  static getOptions() {
    return Object.assign({}, super.getOptions(), {
      indexes: [
        { fields: ['user_id'] },
        { fields: ['user_name'], unique: true },
        { fields: ['email'] },
        { fields: ['status'] },
        { fields: ['ip'] },
        { fields: ['create_time'] },
        { fields: ['user_id', 'status'] },
        { fields: ['create_time', 'status'] },
      ],
    });
  }

  /**
   * Lấy enum status
   */
  static getStatusEnum() {
    return Enums.USER_STATUS;
  }

  /**
   * Transform dữ liệu user
   */
  static transform(row) {
    if (!row) return null;

    return {
      ...row,
      ip: Utils.formatIP(row.ip),
      email: Utils.sanitizeString(row.email),
      phone: Utils.sanitizeString(row.phone),
      createTimeFormatted: row.create_time ? Utils.timestampToDate(row.create_time) : null,
    };
  }

  /**
   * Validate dữ liệu user
   */
  static validate(data) {
    super.validate(data);

    if (data.email && !Utils.isValidEmail(data.email)) {
      throw new Error('Invalid email format');
    }

    if (data.phone && !Utils.isValidPhone(data.phone)) {
      throw new Error('Invalid phone format');
    }

    return true;
  }

  // === QUERY HELPERS (Static methods like MySQL models) ===

  /**
   * Tìm user active
   */
  static async findActive() {
    // Delegate to service
    const UserQueryService = (await import('../../services/userQueryService.js')).default;
    return await UserQueryService.findActive();
  }

  /**
   * Tìm user theo IP
   */
  static async findByIP(ip) {
    const UserQueryService = (await import('../../services/userQueryService.js')).default;
    return await UserQueryService.findByIP(ip);
  }

  /**
   * Tìm user hôm nay
   */
  static async findToday() {
    const UserQueryService = (await import('../../services/userQueryService.js')).default;
    return await UserQueryService.findToday();
  }

  /**
   * Tìm user tháng này
   */
  static async findThisMonth() {
    const UserQueryService = (await import('../../services/userQueryService.js')).default;
    return await UserQueryService.findThisMonth();
  }

  /**
   * Đếm user theo status
   */
  static async countByStatus(status) {
    const UserQueryService = (await import('../../services/userQueryService.js')).default;
    return await UserQueryService.countDocuments({ status });
  }
}
