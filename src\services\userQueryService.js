import MySQL2Service from './mysql2Service.js';
import UserModel from '../models/mysql2/userModel.js';
import { Queries, Utils } from '../models/mysql2/helpers.js';
import logger from '../config/logger.js';

/**
 * Service xử lý các query liên quan đến User
 * Tách logic query ra khỏi model để tuân theo pattern của MySQL models
 */
class UserQueryService {
  static get tableName() {
    return UserModel.tableName;
  }

  /**
   * Build WHERE clause từ conditions
   * @param {Object} conditions - <PERSON><PERSON><PERSON><PERSON> ki<PERSON> query
   * @returns {Object} { whereClause, params }
   */
  static buildWhereClause(conditions) {
    if (!conditions || Object.keys(conditions).length === 0) {
      return { whereClause: '1', params: [] };
    }

    const clauses = [];
    const params = [];

    for (const [key, value] of Object.entries(conditions)) {
      if (key === 'createTime' && typeof value === 'object') {
        // Xử lý range query cho createTime
        if (value.$gte !== undefined) {
          clauses.push('create_time >= ?');
          params.push(value.$gte);
        }
        if (value.$lte !== undefined) {
          clauses.push('create_time <= ?');
          params.push(value.$lte);
        }
        if (value.$gt !== undefined) {
          clauses.push('create_time > ?');
          params.push(value.$gt);
        }
        if (value.$lt !== undefined) {
          clauses.push('create_time < ?');
          params.push(value.$lt);
        }
      } else if (typeof value === 'object' && value !== null) {
        // Xử lý các operator đặc biệt
        if (value.$exists !== undefined) {
          if (value.$exists) {
            clauses.push(`${key} IS NOT NULL`);
          } else {
            clauses.push(`${key} IS NULL`);
          }
        }
        if (value.$nin !== undefined && Array.isArray(value.$nin)) {
          const placeholders = value.$nin.map(() => '?').join(', ');
          clauses.push(`${key} NOT IN (${placeholders})`);
          params.push(...value.$nin);
        }
        if (value.$in !== undefined && Array.isArray(value.$in)) {
          const placeholders = value.$in.map(() => '?').join(', ');
          clauses.push(`${key} IN (${placeholders})`);
          params.push(...value.$in);
        }
      } else if (value !== null && value !== undefined) {
        clauses.push(`${key} = ?`);
        params.push(value);
      }
    }

    return {
      whereClause: clauses.length > 0 ? clauses.join(' AND ') : '1',
      params
    };
  }

  /**
   * Đếm tổng số tài khoản (tương đương estimatedDocumentCount)
   */
  static async estimatedDocumentCount() {
    try {
      return await this.countDocuments();
    } catch (error) {
      logger.error('UserQueryService.estimatedDocumentCount error:', error);
      throw error;
    }
  }

  /**
   * Đếm số tài khoản theo điều kiện
   * @param {Object} conditions - Điều kiện query
   */
  static async countDocuments(conditions = {}) {
    try {
      const { whereClause, params } = this.buildWhereClause(conditions);
      return await MySQL2Service.count(this.tableName, whereClause, params);
    } catch (error) {
      logger.error('UserQueryService.countDocuments error:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách IP duy nhất (tương đương distinct)
   * @param {string} field - Field để lấy distinct
   * @param {Object} conditions - Điều kiện query
   */
  static async distinct(field, conditions = {}) {
    try {
      if (field === 'ip') {
        const { whereClause, params } = this.buildWhereClause(conditions);
        let sql = `SELECT DISTINCT ${field} FROM ${this.tableName}`;
        
        if (whereClause !== '1') {
          sql += ` WHERE ${whereClause}`;
        }
        
        // Thêm điều kiện để loại bỏ IP null hoặc rỗng
        if (whereClause === '1') {
          sql += ` WHERE ${field} IS NOT NULL AND ${field} != ''`;
        } else {
          sql += ` AND ${field} IS NOT NULL AND ${field} != ''`;
        }

        const rows = await MySQL2Service.query(sql, params);
        return rows.map(row => Utils.formatIP(row[field])).filter(ip => ip);
      }
      
      throw new Error(`Distinct field '${field}' is not supported`);
    } catch (error) {
      logger.error('UserQueryService.distinct error:', error);
      throw error;
    }
  }

  /**
   * Lấy thông tin user theo ID
   * @param {number} userId - ID của user
   */
  static async findByUserId(userId) {
    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE user_id = ?`;
      const result = await MySQL2Service.getOne(sql, [userId]);
      return UserModel.transform(result);
    } catch (error) {
      logger.error('UserQueryService.findByUserId error:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách user theo điều kiện
   * @param {Object} conditions - Điều kiện query
   * @param {Object} options - Tùy chọn (limit, offset, orderBy)
   */
  static async find(conditions = {}, options = {}) {
    try {
      const { whereClause, params } = this.buildWhereClause(conditions);
      let sql = `SELECT * FROM ${this.tableName}`;
      
      if (whereClause !== '1') {
        sql += ` WHERE ${whereClause}`;
      }

      if (options.orderBy) {
        sql += ` ORDER BY ${options.orderBy}`;
      }

      if (options.limit) {
        sql += ` LIMIT ${options.limit}`;
        if (options.offset) {
          sql += ` OFFSET ${options.offset}`;
        }
      }

      const rows = await MySQL2Service.query(sql, params);
      return rows.map(row => UserModel.transform(row));
    } catch (error) {
      logger.error('UserQueryService.find error:', error);
      throw error;
    }
  }

  // === QUERY HELPERS USING PATTERN ===

  /**
   * Tìm user active
   */
  static async findActive(options = {}) {
    return await this.find(Queries.activeUsers().where, options);
  }

  /**
   * Tìm user theo IP
   */
  static async findByIP(ip, options = {}) {
    return await this.find(Queries.byIP(ip).where, options);
  }

  /**
   * Tìm user trong khoảng thời gian
   */
  static async findByTimeRange(startTime, endTime, options = {}) {
    return await this.find(Queries.byTimeRange(startTime, endTime).where, options);
  }

  /**
   * Tìm user hôm nay
   */
  static async findToday(options = {}) {
    return await this.find(Queries.today().where, options);
  }

  /**
   * Tìm user tháng này
   */
  static async findThisMonth(options = {}) {
    return await this.find(Queries.thisMonth().where, options);
  }

  /**
   * Tìm user với IP hợp lệ
   */
  static async findWithValidIP(options = {}) {
    return await this.find(Queries.withValidIP().where, options);
  }

  /**
   * Đếm user active
   */
  static async countActive() {
    return await this.countDocuments(Queries.activeUsers().where);
  }

  /**
   * Đếm user hôm nay
   */
  static async countToday() {
    return await this.countDocuments(Queries.today().where);
  }

  /**
   * Đếm user tháng này
   */
  static async countThisMonth() {
    return await this.countDocuments(Queries.thisMonth().where);
  }
}

export default UserQueryService;
