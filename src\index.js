import 'dotenv/config'; // Tải biến môi trường từ tệp .env

import './config/database.js';

import express from 'express';
import HTTPStatus from 'http-status';
// import serverAuthMiddleware from './middleware/serverAuthMiddleware.js'; // Import server-to-server auth middleware
import apiRoutes from './routes/index.js'; // Import routes
import configureExpress from './config/express.js'; // Import cấu hình express
import logger from './config/logger.js'; // Import logger
import AppError from './services/errorService.js'; // Import AppError
import { errorResponse } from './utils/apiResponseUltil.js';

logger.info('🚀 Starting application...');

const app = express();
const PORT = process.env.PORT || 3000; // Sử dụng cổng từ .env hoặc mặc định 3000

// Middleware cơ bản
configureExpress(app); // Cấu hình express với các middleware như helmet, cors, morgan, etc.

// Middleware xác thực server-to-server - Áp dụng cho tất cả các route
// app.use('/api', serverAuthMiddleware);

// Routes API
app.use('/api', apiRoutes);

// Route not found handler - thêm middleware này để xử lý route không tìm thấy
app.use((req, res, next) => {
  const err = new AppError(
    `Lỗi API: ${req.originalUrl}`,
    res?.statusCode ? res.statusCode : HTTPStatus.NOT_FOUND
  );
  next(err);
});

// eslint-disable-next-line no-unused-vars
app.use((err, _req, res, next) => {
  logger.error(err);
  // Kiểm tra nếu lỗi là AppError
  const statusCode =
    err instanceof AppError && err.statusCode ? err.statusCode : HTTPStatus.INTERNAL_SERVER_ERROR;
  const message = err instanceof AppError ? err.message : 'Đã có lỗi xảy ra!';

  res
    .status(statusCode)
    .json(errorResponse(message, null, process.env.NODE_ENV === 'development' ? err.stack : {}));
});

// Khởi động server
const server = app.listen(PORT, () => {
  logger.info(`Server đang chạy trên cổng ${PORT}`);
});
// Convert SERVER_TIMEOUT from string to integer, with a default of 30000 if not set or invalid
server.timeout = parseInt(process.env.SERVER_TIMEOUT, 10) || 30000; // Thay đổi timeout của server nếu cần
