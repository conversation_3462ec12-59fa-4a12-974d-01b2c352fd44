import { Model } from 'sequelize';

/**
 * Base Model - Model cơ bản cho tất cả các bảng
 */
export default class BaseModel extends Model {
  /**
   * Khởi tạo model với Sequelize
   */
  static init(sequelize, DataTypes) {
    const defaultOptions = {
      sequelize: sequelize,
      modelName: this.name,
      tableName: this.tableName,
      timestamps: true,
      paranoid: true,
      underscored: true,
      freezeTableName: true,
    };

    const customOptions = this.getOptions() || {};
    const options = Object.assign({}, defaultOptions, customOptions);

    return super.init(this.getFields(DataTypes), options);
  }

  /**
   * Đ<PERSON>nh nghĩa các field cơ bản
   */
  static getFields(DataTypes) {
    return {
      id: {
        type: DataTypes.BIGINT,
        primaryKey: true,
        autoIncrement: true,
        comment: 'ID chính',
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: 'created_at',
        comment: 'Thời gian tạo',
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
        field: 'updated_at',
        comment: 'Thời gian cập nhật',
      },
      deletedAt: {
        type: DataTypes.DATE,
        allowNull: true,
        field: 'deleted_at',
        comment: 'Thời gian xóa',
      },
    };
  }

  /**
   * Cấu hình tùy chọn
   */
  static getOptions() {
    return {
      indexes: [{ fields: ['created_at'] }],
      tableOptions: {
        engine: 'InnoDB',
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
      },
    };
  }

  /**
   * Tìm theo ID
   */
  static async findById(id, options = {}) {
    const record = await this.findByPk(id, options);
    if (!record) {
      throw new Error(`${this.name} với ID ${id} không tồn tại`);
    }
    return record;
  }

  /**
   * Tạo record mới
   */
  static async create(data, options = {}) {
    try {
      return await super.create(data, options);
    } catch (error) {
      throw new Error(`Lỗi tạo ${this.name}: ${error.message}`);
    }
  }

  /**
   * Cập nhật record
   */
  static async update(data, options = {}) {
    try {
      return await super.update(data, options);
    } catch (error) {
      throw new Error(`Lỗi cập nhật ${this.name}: ${error.message}`);
    }
  }

  /**
   * Xóa record
   */
  static async delete(options = {}) {
    try {
      return await this.destroy(options);
    } catch (error) {
      throw new Error(`Lỗi xóa ${this.name}: ${error.message}`);
    }
  }

  /**
   * Đếm số lượng record
   */
  static async count(options = {}) {
    try {
      return await super.count(options);
    } catch (error) {
      throw new Error(`Lỗi đếm ${this.name}: ${error.message}`);
    }
  }

  /**
   * Tìm tất cả record
   */
  static async findAll(options = {}) {
    try {
      return await super.findAll(options);
    } catch (error) {
      throw new Error(`Lỗi tìm ${this.name}: ${error.message}`);
    }
  }

  /**
   * Tìm một record
   */
  static async findOne(options = {}) {
    try {
      return await super.findOne(options);
    } catch (error) {
      throw new Error(`Lỗi tìm ${this.name}: ${error.message}`);
    }
  }

  /**
   * Phân trang
   */
  static async paginate(page = 1, limit = 10, options = {}) {
    const offset = (page - 1) * limit;
    const query = Object.assign({}, options, {
      limit: limit,
      offset: offset,
    });
    const { count, rows } = await this.findAndCountAll(query);

    return {
      data: rows,
      pagination: {
        page,
        limit,
        total: count,
        totalPages: Math.ceil(count / limit),
        hasNext: page < Math.ceil(count / limit),
        hasPrev: page > 1,
      },
    };
  }
}
