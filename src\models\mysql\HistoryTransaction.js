import BaseModel from './BaseModel.js';
import { CommonFields, CommonScopes } from './ModelUtils.js';

/**
 * HistoryTransaction Model
 * Represents transaction history records
 */
export default class HistoryTransaction extends BaseModel {
  static tableName = 'history_transactions';

  static getAttributes(DataTypes) {
    return Object.assign({}, super.getAttributes(DataTypes), {
      // Account information
      accountId: CommonFields.accountId(DataTypes),

      // Foreign key to transactions table
      transactionId: {
        type: DataTypes.BIGINT,
        allowNull: false,
        field: 'transaction_id',
        comment: 'Reference to transactions table',
        references: {
          model: 'transactions',
          key: 'id',
        },
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      },

      // Action information
      action: {
        type: DataTypes.TINYINT,
        allowNull: false,
        comment: 'Account action: 0=Unknown, 1=Deposit, 2=Withdraw, 3=Transfer, 4=Payment',
        validate: {
          isIn: [[0, 1, 2, 3, 4]],
        },
      },

      // Amount information
      amount: CommonFields.amount(DataTypes),

      // Balance information
      balanceBefore: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0,
        field: 'balance_before',
        comment: 'Balance before transaction',
        validate: {
          min: 0,
        },
      },
      balanceAfter: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        defaultValue: 0,
        field: 'balance_after',
        comment: 'Balance after transaction',
        validate: {
          min: 0,
        },
      },

      // Description
      description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Transaction description',
      },

      // Additional data
      metaData: CommonFields.metaData(DataTypes),
    });
  }

  static getOptions() {
    return Object.assign({}, super.getOptions(), {
      indexes: [
        {
          fields: ['account_id'],
        },
        {
          fields: ['transaction_id'],
        },
        {
          fields: ['action'],
        },
        {
          fields: ['created_at'],
        },
        {
          fields: ['account_id', 'action'],
        },
        {
          fields: ['account_id', 'created_at'],
        },
      ],
    });
  }

  /**
   * Define associations with other models
   * @param {Object} models - All models
   */
  static associate(models) {
    // Many-to-one relationship with Transaction
    this.belongsTo(models.Transaction, {
      foreignKey: 'transactionId',
      as: 'transaction',
      onDelete: 'CASCADE',
      onUpdate: 'CASCADE',
    });
  }

  /**
   * Get account action enum
   * @returns {Object} Action enum
   */
  static getActionEnum() {
    return {
      UNKNOWN: 0,
      DEPOSIT: 1,
      WITHDRAW: 2,
      TRANSFER: 3,
      PAYMENT: 4,
    };
  }

  // Scope methods using CommonScopes
  static scopeDeposits() {
    return { where: { action: this.getActionEnum().DEPOSIT } };
  }

  static scopeWithdrawals() {
    return { where: { action: this.getActionEnum().WITHDRAW } };
  }

  static scopeTransfers() {
    return { where: { action: this.getActionEnum().TRANSFER } };
  }

  static scopePayments() {
    return { where: { action: this.getActionEnum().PAYMENT } };
  }

  static scopeByAccount(accountId) {
    return CommonScopes.byAccount(accountId);
  }

  static scopeByTransaction(transactionId) {
    return { where: { transactionId } };
  }

  static scopeByAction(action) {
    return { where: { action } };
  }

  static scopeByAmountRange(minAmount, maxAmount) {
    return CommonScopes.byAmountRange(minAmount, maxAmount);
  }

  static scopeToday() {
    return CommonScopes.today();
  }

  static scopeThisMonth() {
    return CommonScopes.thisMonth();
  }
}
