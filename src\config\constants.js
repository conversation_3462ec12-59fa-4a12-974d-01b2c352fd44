// Application constants and environment variables configuration

// Server configuration
export const SERVER = {
  PORT: process.env.PORT || 3000,
  ENV: process.env.NODE_ENV || 'development',
  TIMEOUT: parseInt(process.env.SERVER_TIMEOUT, 10) || 30000,
  SECRET_KEY: process.env.SERVER_SECRET_KEY || 'your-default-secret-key',
};

// Database configuration
export const DATABASE = {
  // MySQL
  MYSQL: {
    HOST: process.env.MYSQL_HOST || 'localhost',
    PORT: Number(process.env.MYSQL_PORT) || 3306,
    USER: process.env.MYSQL_USER,
    PASSWORD: process.env.MYSQL_PASSWORD,
    DATABASE: process.env.MYSQL_DATABASE,
    CONNECTION_LIMIT: Number(process.env.MYSQL_CONNECTION_LIMIT) || 10,
    ACQUIRE_TIMEOUT: Number(process.env.MYSQL_ACQUIRE_TIMEOUT) || 60000,
  },

  // MySQL2 (Sabo ID replacement)
  MYSQL2: {
    HOST: process.env.MYSQL2_HOST || 'localhost',
    PORT: Number(process.env.MYSQL2_PORT) || 5307,
    USER: process.env.MYSQL2_USER,
    PASSWORD: process.env.MYSQL2_PASSWORD,
    DATABASE: process.env.MYSQL2_DATABASE,
    CONNECTION_LIMIT: Number(process.env.MYSQL2_CONNECTION_LIMIT) || 10,
    ACQUIRE_TIMEOUT: Number(process.env.MYSQL2_ACQUIRE_TIMEOUT) || 60000,
  },
};

// JWT Configuration
export const JWT = {
  SECRET_KEY: process.env.JWT_SECRET_KEY || 'your-jwt-secret-key',
  EXPIRES_IN: process.env.JWT_EXPIRES_IN || '24h',
};

// Time Periods for statistics
export const TIME_PERIODS = {
  TODAY: 1,
  YESTERDAY: 2,
  THIS_WEEK: 3,
  LAST_7_DAYS: 4,
  THIS_MONTH: 5,
};

// Data Types for statistics
export const DATA_TYPES = {
  ACCOUNTS: 1,
  IPS: 2,
  REVENUE: 3,
  TRANSACTIONS: 4,
};

// Error handling
export const ERROR_MESSAGES = {
  INVALID_DATA_TYPE: 'Loại dữ liệu không hợp lệ. Phải là 1-4.',
  DB_CONNECTION_ERROR: 'Lỗi kết nối cơ sở dữ liệu',
  UNAUTHORIZED: 'Không có quyền truy cập',
};

// Logging
export const LOGGING = {
  LEVEL: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  SENTRY_DSN: process.env.RAVEN_ID,
};

export const isProduction = SERVER.ENV === 'production';
export const isDevelopment = SERVER.ENV === 'development';
