import { Op } from 'sequelize';

/**
 * Common enums used across models
 */
export const CommonEnums = {
  STATUS: {
    PENDING: 0,
    COMPLETED: 1,
    FAILED: 2,
    CANCELLED: 3,
  },
  PAYMENT_METHOD: {
    UNKNOWN: 0,
    BANK: 1,
    CARD: 2,
    WALLET: 3,
    CRYPTO: 4,
  },
  ACTION: {
    UNKNOWN: 0,
    LOGIN: 1,
    LOGOUT: 2,
    TRANSACTION: 3,
    PROFILE_UPDATE: 4,
    SECURITY_CHANGE: 5,
  },
};

/**
 * Common scope methods
 */
export const CommonScopes = {
  /**
   * Date range scope
   */
  byDateRange(startDate, endDate) {
    return {
      where: {
        createdAt: {
          [Op.between]: [startDate, endDate],
        },
      },
    };
  },

  /**
   * Today scope
   */
  today() {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
    return this.byDateRange(startOfDay, endOfDay);
  },

  /**
   * This month scope
   */
  thisMonth() {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    return this.byDateRange(startOfMonth, endOfMonth);
  },

  /**
   * Amount range scope
   */
  byAmountRange(minAmount, maxAmount) {
    return {
      where: {
        amount: { [Op.between]: [minAmount, maxAmount] },
      },
    };
  },

  /**
   * By account scope
   */
  byAccount(accountId) {
    return { where: { accountId } };
  },

  /**
   * By status scope
   */
  byStatus(status) {
    return { where: { status } };
  },
};

/**
 * Common validation rules
 */
export const CommonValidations = {
  amount: {
    min: 0,
  },
  status: {
    isIn: [[0, 1, 2, 3]],
  },
  paymentMethod: {
    isIn: [[0, 1, 2, 3, 4]],
  },
};

/**
 * Common field definitions
 */
export const CommonFields = {
  accountId: (DataTypes) => ({
    type: DataTypes.STRING(255),
    allowNull: false,
    field: 'account_id',
    comment: 'Account ID',
  }),

  accountName: (DataTypes) => ({
    type: DataTypes.STRING(255),
    allowNull: false,
    field: 'account_name',
    comment: 'Account name',
  }),

  amount: (DataTypes) => ({
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Transaction amount',
    validate: CommonValidations.amount,
  }),

  coinAmount: (DataTypes) => ({
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
    field: 'coin_amount',
    comment: 'Coin amount',
    validate: CommonValidations.amount,
  }),

  status: (DataTypes) => ({
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0,
    comment: 'Status',
    validate: CommonValidations.status,
  }),

  paymentMethod: (DataTypes) => ({
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0,
    field: 'payment_method',
    comment: 'Payment method',
    validate: CommonValidations.paymentMethod,
  }),

  metaData: (DataTypes) => ({
    type: DataTypes.JSON,
    allowNull: true,
    field: 'meta_data',
    comment: 'Additional metadata as JSON',
  }),
};
