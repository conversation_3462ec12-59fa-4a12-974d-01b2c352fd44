import { getUserAnalytics } from '../services/userService.js';
import { successResponse } from '../utils/apiResponseUltil.js';
import logger from '../config/logger.js';

/**
 * Handle user analytics API request
 */
export const getUserAnalyticsController = async (req, res, next) => {
  try {
    const { startDate, endDate } = req.query;

    // Validate date inputs if provided
    if (startDate && isNaN(new Date(startDate).getTime())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid startDate format. Use YYYY-MM-DD',
      });
    }

    if (endDate && isNaN(new Date(endDate).getTime())) {
      return res.status(400).json({
        success: false,
        message: 'Invalid endDate format. Use YYYY-MM-DD',
      });
    }

    const filters = { startDate, endDate };
    const analytics = await getUserAnalytics(filters);

    return res.json(successResponse('User analytics generated successfully', analytics));
  } catch (error) {
    logger.error('Error in user analytics controller:', error);
    return next(error);
  }
};
