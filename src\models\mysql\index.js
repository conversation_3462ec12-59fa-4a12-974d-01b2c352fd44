import { Sequelize } from 'sequelize';

// Import all models
import BaseModel from './BaseModel.js';
import Transaction from './Transaction.js';
import HistoryTransaction from './HistoryTransaction.js';
import McoinTransaction from './McoinTransaction.js';
import AccountWallet from './AccountWallet.js';
import AccountCard from './AccountCard.js';
import AccountLog from './AccountLog.js';

/**
 * Initialize all models with Sequelize instance
 * @param {Sequelize} sequelize - Sequelize instance
 * @returns {Object} Object containing all initialized models
 */
export function initializeModels(sequelize) {
  const { DataTypes } = Sequelize;

  try {
    // Initialize all models
    const models = {
      Transaction: Transaction.init(sequelize, DataTypes),
      HistoryTransaction: HistoryTransaction.init(sequelize, DataTypes),
      McoinTransaction: McoinTransaction.init(sequelize, DataTypes),
      AccountWallet: AccountWallet.init(sequelize, DataTypes),
      AccountCard: AccountCard.init(sequelize, DataTypes),
      AccountLog: AccountLog.init(sequelize, DataTypes),
    };

    // Set up associations
    Object.values(models).forEach((model) => {
      if (model.associate) {
        model.associate(models);
      }
    });

    console.log('✅ All models initialized successfully');
    return models;
  } catch (error) {
    console.error('❌ Error initializing models:', error.message);
    throw new Error(`Failed to initialize models: ${error.message}`);
  }
}

/**
 * Sync all models with database
 * @param {Sequelize} sequelize - Sequelize instance
 * @param {Object} options - Sync options
 */
export async function syncModels(sequelize, options = {}) {
  try {
    const models = initializeModels(sequelize);

    const syncOptions = Object.assign(
      {
        force: false, // Set to true to drop and recreate tables
        alter: false, // Set to true to alter existing tables
      },
      options
    );

    await sequelize.sync(syncOptions);
    console.log('✅ All models synchronized with database');

    return models;
  } catch (error) {
    console.error('❌ Error syncing models:', error.message);
    throw new Error(`Failed to sync models: ${error.message}`);
  }
}

/**
 * Get all model classes (not initialized)
 * @returns {Object} Object containing all model classes
 */
export function getModelClasses() {
  return {
    BaseModel,
    Transaction,
    HistoryTransaction,
    McoinTransaction,
    AccountWallet,
    AccountCard,
    AccountLog,
  };
}

/**
 * Validate model associations
 * @param {Object} models - Initialized models
 * @returns {boolean} Validation result
 */
export function validateAssociations(models) {
  try {
    const modelEntries = Object.entries(models);
    console.log(`🔍 Validating associations for ${modelEntries.length} models...`);

    // Check if all models have proper associations
    for (const [modelName, model] of modelEntries) {
      if (model && model.associations) {
        console.log(`  ✅ ${modelName}: ${Object.keys(model.associations).length} associations`);
      } else {
        console.log(`  ⚠️  ${modelName}: No associations defined`);
      }
    }

    return true;
  } catch (error) {
    console.error('❌ Error validating associations:', error.message);
    return false;
  }
}

/**
 * Get model statistics
 * @param {Object} models - Initialized models
 * @returns {Object} Model statistics
 */
export async function getModelStats(models) {
  try {
    const stats = {};

    for (const [modelName, model] of Object.entries(models)) {
      try {
        const count = await model.count();
        Object.defineProperty(stats, modelName, {
          value: {
            recordCount: count,
            tableName: model.tableName,
            associations: Object.keys(model.associations || {}).length,
          },
          writable: true,
          enumerable: true,
        });
      } catch (error) {
        Object.defineProperty(stats, modelName, {
          value: {
            error: error.message,
            tableName: model.tableName,
          },
          writable: true,
          enumerable: true,
        });
      }
    }

    return stats;
  } catch (error) {
    console.error('❌ Error getting model stats:', error.message);
    throw new Error(`Failed to get model stats: ${error.message}`);
  }
}

/**
 * Test database connection and models
 * @param {Sequelize} sequelize - Sequelize instance
 * @returns {Promise<boolean>} Test result
 */
export async function testConnection(sequelize) {
  try {
    console.log('🔍 Testing database connection...');

    // Test connection
    await sequelize.authenticate();
    console.log('✅ Database connection successful');

    // Initialize models
    const models = initializeModels(sequelize);

    // Validate associations
    validateAssociations(models);

    // Get model stats
    const stats = await getModelStats(models);
    console.log('📊 Model statistics:', stats);

    return true;
  } catch (error) {
    console.error('❌ Database connection test failed:', error.message);
    return false;
  }
}

// Export model classes
export {
  BaseModel,
  Transaction,
  HistoryTransaction,
  McoinTransaction,
  AccountWallet,
  AccountCard,
  AccountLog,
};
