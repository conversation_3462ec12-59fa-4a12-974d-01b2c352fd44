import { mysql2Pool } from '../config/mysql2.js';
import logger from '../config/logger.js';

class MySQL2Service {
  // Thực thi query đơn giản với nhiều tùy chọn
  static async query(sql, params = [], options = {}) {
    try {
      const { debug = false } = options;

      if (debug) {
        logger.debug(`🔍 MySQL2 Query: ${sql}`, { params });
      }

      const [rows] = await mysql2Pool.execute(sql, params);

      if (debug) {
        logger.debug(`✅ MySQL2 Query Result: ${rows.length} rows`);
      }

      return rows;
    } catch (error) {
      logger.error('❌ MySQL2 Query Error:', {
        sql,
        params,
        error: error.message,
        code: error.code,
      });
      throw error;
    }
  }

  // Lấy một bản ghi với validation
  static async getOne(sql, params = [], options = {}) {
    try {
      const rows = await this.query(sql, params, options);
      const result = rows[0] || null;

      if (options.required && !result) {
        throw new Error(`Record not found for query: ${sql}`);
      }

      return result;
    } catch (error) {
      logger.error('❌ MySQL2 GetOne Error:', error);
      throw error;
    }
  }

  // Đếm số bản ghi
  static async count(table, where = '1', params = [], options = {}) {
    try {
      const { distinct = null, alias = 'total' } = options;

      let sql = `SELECT COUNT(${distinct ? `DISTINCT ${distinct}` : '*'}) as ${alias} FROM ${table}`;

      if (where && where !== '1') {
        sql += ` WHERE ${where}`;
      }

      const result = await this.getOne(sql, params, options);
      if (!result) return 0;

      // Safe access to result properties
      return Number(result[alias]) || 0;
    } catch (error) {
      logger.error('❌ MySQL2 Count Error:', error);
      throw error;
    }
  }

  // Lấy danh sách IP duy nhất
  static async distinctIPs(startTime = null, endTime = null) {
    try {
      let sql = 'SELECT DISTINCT ip FROM user WHERE ip IS NOT NULL AND ip != ""';
      const params = [];

      if (startTime && endTime) {
        sql += ' AND create_time BETWEEN ? AND ?';
        params.push(startTime, endTime);
      }

      const rows = await this.query(sql, params);
      return rows.map(row => row.ip);
    } catch (error) {
      logger.error('❌ MySQL2 Distinct IPs Error:', error);
      throw error;
    }
  }

  // Kiểm tra kết nối
  static async testConnection() {
    try {
      const connection = await mysql2Pool.getConnection();
      connection.release();
      return true;
    } catch (error) {
      logger.error('❌ MySQL2 Connection Test Failed:', error);
      return false;
    }
  }
}

export default MySQL2Service;
