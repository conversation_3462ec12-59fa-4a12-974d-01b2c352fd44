import { Router } from 'express';

import UserRoutes from './userRoute.js';
import OverviewRoutes from './overviewRoute.js';
import StatisticsRoutes from './statisticsRoute.js';

// Middlewares
import logErrorService from '../services/logService.js';

const routes = new Router();

routes.use('/users', UserRoutes);
routes.use('/overview', OverviewRoutes);
routes.use('/statistics', StatisticsRoutes);

routes.use(logErrorService);

export default routes;
