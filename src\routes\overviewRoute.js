import express from 'express';
import { getOverview, getOverviewByDate } from '../controllers/overviewController.js';

const router = express.Router();

/**
 * GET /api/overview
 * L<PERSON>y thống kê tổng quan hệ thống
 */
router.get('/', getOverview);

/**
 * POST /api/overview/by-date
 * L<PERSON>y thống kê tổng quan theo khoảng thời gian và loại dữ liệu
 */
router.post('/by-date', getOverviewByDate);

export default router;
