import { Op } from 'sequelize';

/**
 * <PERSON><PERSON><PERSON> giá trị enum chung
 */
export const Enums = {
  STATUS: {
    PENDING: 0,
    COMPLETED: 1,
    FAILED: 2,
    CANCELLED: 3,
  },
  PAYMENT_METHOD: {
    UNKNOWN: 0,
    BANK: 1,
    CARD: 2,
    WALLET: 3,
    CRYPTO: 4,
  },
  ACTION: {
    UNKNOWN: 0,
    LOGIN: 1,
    LOGOUT: 2,
    TRANSACTION: 3,
    PROFILE_UPDATE: 4,
    SECURITY_CHANGE: 5,
  },
};

/**
 * Các field chung
 */
export const Fields = {
  accountId: (DataTypes) => ({
    type: DataTypes.STRING(255),
    allowNull: false,
    field: 'account_id',
    comment: 'ID tài khoản',
  }),

  accountName: (DataTypes) => ({
    type: DataTypes.STRING(255),
    allowNull: false,
    field: 'account_name',
    comment: 'Tên tài khoản',
  }),

  amount: (DataTypes) => ({
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    comment: 'Số tiền',
    validate: { min: 0 },
  }),

  coinAmount: (DataTypes) => ({
    type: DataTypes.DECIMAL(10, 2),
    allowNull: false,
    defaultValue: 0,
    field: 'coin_amount',
    comment: 'Số coin',
    validate: { min: 0 },
  }),

  status: (DataTypes) => ({
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0,
    comment: 'Trạng thái',
    validate: { isIn: [[0, 1, 2, 3]] },
  }),

  paymentMethod: (DataTypes) => ({
    type: DataTypes.TINYINT,
    allowNull: false,
    defaultValue: 0,
    field: 'payment_method',
    comment: 'Phương thức thanh toán',
    validate: { isIn: [[0, 1, 2, 3, 4]] },
  }),

  metaData: (DataTypes) => ({
    type: DataTypes.JSON,
    allowNull: true,
    field: 'meta_data',
    comment: 'Dữ liệu bổ sung',
  }),
};

/**
 * Các query chung
 */
export const Queries = {
  byAccount(accountId) {
    return { where: { accountId } };
  },

  byStatus(status) {
    return { where: { status } };
  },

  byDateRange(startDate, endDate) {
    return {
      where: {
        createdAt: { [Op.between]: [startDate, endDate] },
      },
    };
  },

  today() {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
    return this.byDateRange(startOfDay, endOfDay);
  },

  thisMonth() {
    const today = new Date();
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    return this.byDateRange(startOfMonth, endOfMonth);
  },
};
