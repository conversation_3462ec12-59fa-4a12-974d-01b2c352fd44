import BaseModel from './BaseModel.js';
import { Enums, Fields, Queries, Utils } from './helpers.js';
import MySQL2Service from '../../services/mysql2Service.js';
import logger from '../../config/logger.js';

/**
 * Model Profile - <PERSON><PERSON><PERSON>n lý thông tin hồ sơ người dùng
 */
class ProfileModel extends BaseModel {
  static get tableName() {
    return 'profile';
  }

  constructor() {
    super();
    this.tableName = ProfileModel.tableName;
  }

  /**
   * <PERSON><PERSON>nh nghĩa các field của bảng profile
   */
  static getFields() {
    return [
      ...super.getBaseFields(),
      'user_id',
      'first_name',
      'last_name', 
      'birth_date',
      'gender',
      'avatar',
      'bio',
      'status'
    ];
  }

  /**
   * Lấy enum status
   */
  static getStatusEnum() {
    return Enums.PROFILE_STATUS;
  }

  /**
   * Lấy enum gender
   */
  static getGenderEnum() {
    return Enums.GENDER;
  }

  /**
   * Transform dữ liệu profile
   */
  transform(row) {
    if (!row) return null;
    
    return {
      ...row,
      first_name: Utils.sanitizeString(row.first_name),
      last_name: Utils.sanitizeString(row.last_name),
      bio: Utils.sanitizeString(row.bio),
      full_name: row.first_name && row.last_name ? 
        `${row.first_name} ${row.last_name}` : null,
      age: row.birth_date ? this.calculateAge(row.birth_date) : null,
    };
  }

  /**
   * Tính tuổi từ ngày sinh
   */
  calculateAge(birthDate) {
    if (!birthDate) return null;
    
    const today = new Date();
    const birth = new Date(birthDate);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    
    return age;
  }

  /**
   * Validate dữ liệu profile
   */
  validate(data) {
    super.validate(data);
    
    if (data.birth_date) {
      const birthDate = new Date(data.birth_date);
      const today = new Date();
      
      if (birthDate >= today) {
        throw new Error('Birth date must be in the past');
      }
      
      const age = this.calculateAge(data.birth_date);
      if (age < 13 || age > 120) {
        throw new Error('Age must be between 13 and 120');
      }
    }
    
    if (data.gender !== undefined && !Object.values(Enums.GENDER).includes(data.gender)) {
      throw new Error('Invalid gender value');
    }
    
    return true;
  }

  /**
   * Đếm tổng số profile
   */
  async estimatedDocumentCount() {
    try {
      return await this.countDocuments();
    } catch (error) {
      logger.error('ProfileModel.estimatedDocumentCount error:', error);
      throw error;
    }
  }

  /**
   * Đếm số profile theo điều kiện
   * @param {Object} conditions - Điều kiện query từ helpers
   */
  async countDocuments(conditions = {}) {
    try {
      const { whereClause, params } = this.buildWhereClause(conditions);
      return await MySQL2Service.count(this.tableName, whereClause, params);
    } catch (error) {
      logger.error('ProfileModel.countDocuments error:', error);
      throw error;
    }
  }

  /**
   * Lấy profile theo user ID
   * @param {number} userId - ID của user
   */
  async findByUserId(userId) {
    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE user_id = ?`;
      const result = await MySQL2Service.getOne(sql, [userId]);
      return this.transform(result);
    } catch (error) {
      logger.error('ProfileModel.findByUserId error:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách profile theo điều kiện
   */
  async find(conditions = {}, options = {}) {
    try {
      const { whereClause, params } = this.buildWhereClause(conditions);
      let sql = `SELECT * FROM ${this.tableName}`;
      
      if (whereClause !== '1') {
        sql += ` WHERE ${whereClause}`;
      }

      const orderByClause = this.buildOrderByClause(options.orderBy);
      if (orderByClause) {
        sql += ` ORDER BY ${orderByClause}`;
      }

      const limitClause = this.buildLimitClause(options.limit, options.offset);
      if (limitClause) {
        sql += limitClause;
      }

      const rows = await MySQL2Service.query(sql, params);
      return this.transformMany(rows);
    } catch (error) {
      logger.error('ProfileModel.find error:', error);
      throw error;
    }
  }

  // === QUERY HELPERS USING PATTERN ===

  /**
   * Tìm profile complete
   */
  async findComplete(options = {}) {
    return await this.find(
      Queries.byStatus(Enums.PROFILE_STATUS.COMPLETE).where, 
      options
    );
  }

  /**
   * Tìm profile verified
   */
  async findVerified(options = {}) {
    return await this.find(
      Queries.byStatus(Enums.PROFILE_STATUS.VERIFIED).where, 
      options
    );
  }

  /**
   * Đếm profile complete
   */
  async countComplete() {
    return await this.countDocuments(
      Queries.byStatus(Enums.PROFILE_STATUS.COMPLETE).where
    );
  }

  /**
   * Đếm profile verified
   */
  async countVerified() {
    return await this.countDocuments(
      Queries.byStatus(Enums.PROFILE_STATUS.VERIFIED).where
    );
  }
}

// Export instance của class
export default new ProfileModel();
