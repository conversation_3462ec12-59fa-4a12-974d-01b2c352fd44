import HTTPStatus from 'http-status';

/**
 * Custom Application Error class
 * Extends the standard Error object with additional properties
 * useful for error handling and logging
 */
class AppError extends Error {
  /**
   * Create a new AppError
   * @param {string} message - Error message
   * @param {number} statusCode - HTTP status code (default: 500)
   * @param {boolean} isOperational - Whether this is an operational error (default: true)
   * @param {object} metadata - Additional data to attach to the error
   */
  constructor(
    message,
    statusCode = HTTPStatus.INTERNAL_SERVER_ERROR,
    isOperational = true,
    metadata = {}
  ) {
    super(message);
    this.name = this.constructor.name;
    this.statusCode = statusCode;
    this.isOperational = isOperational; // Indicates if this is an operational error that we can anticipate
    this.metadata = metadata;
    this.date = new Date();

    // Capture stack trace, excluding the constructor call from the stack
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Database connection error
 */
class DatabaseConnectionError extends AppError {
  constructor(message = 'Database connection error', metadata = {}) {
    super(message, HTTPStatus.INTERNAL_SERVER_ERROR, true, metadata);
  }
}

/**
 * Configuration error (e.g., missing environment variables)
 */
class ConfigurationError extends AppError {
  constructor(message = 'Configuration error', metadata = {}) {
    super(message, HTTPStatus.INTERNAL_SERVER_ERROR, true, metadata);
  }
}

/**
 * Authentication error
 */
class AuthenticationError extends AppError {
  constructor(message = 'Authentication failed', metadata = {}) {
    super(message, HTTPStatus.UNAUTHORIZED, true, metadata);
  }
}

/**
 * Authorization error
 */
class AuthorizationError extends AppError {
  constructor(message = 'Not authorized', metadata = {}) {
    super(message, HTTPStatus.FORBIDDEN, true, metadata);
  }
}

/**
 * Validation error
 */
class ValidationError extends AppError {
  constructor(message = 'Validation failed', metadata = {}) {
    super(message, HTTPStatus.BAD_REQUEST, true, metadata);
  }
}

/**
 * Class for required error
 *
 * @class RequiredError
 */
export class RequiredError {
  /**
   * Make error pretty
   *
   * @static
   * @param {Array} errors - Array of error Object
   * @returns {Object} - errors - Pretty Object transform
   */
  static makePretty(errors) {
    return errors.reduce((obj, error) => {
      const nObj = obj;
      nObj[error.field] = error.messages[0].replace(/"/g, '');
      return nObj;
    }, {});
  }
}

export {
  AppError,
  DatabaseConnectionError,
  ConfigurationError,
  AuthenticationError,
  AuthorizationError,
  ValidationError,
};

export default AppError;
