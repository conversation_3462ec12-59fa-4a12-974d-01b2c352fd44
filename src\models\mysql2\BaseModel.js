/**
 * Base Model cho MySQL2 - Tương tự như BaseModel của MySQL
 * Chỉ chứa định nghĩa cấu trúc, không có logic query
 */
export default class BaseModel {
  /**
   * Tên bảng - override trong class con
   */
  static get tableName() {
    throw new Error('tableName must be defined in child class');
  }

  /**
   * Các field cơ bản cho tất cả bảng
   */
  static getBaseFields() {
    return [
      'id',
      'created_at',
      'updated_at',
      'deleted_at'
    ];
  }

  /**
   * Các field của bảng - override trong class con
   */
  static getFields() {
    return this.getBaseFields();
  }

  /**
   * C<PERSON>u hình tùy chọn - override trong class con
   */
  static getOptions() {
    return {
      indexes: [{ fields: ['created_at'] }],
      tableOptions: {
        engine: 'InnoDB',
        charset: 'utf8mb4',
        collate: 'utf8mb4_unicode_ci',
      },
    };
  }

  /**
   * Lấy enum status - override trong class con nếu cần
   */
  static getStatusEnum() {
    return {};
  }

  /**
   * Associations - override trong class con nếu cần
   */
  static associate(models) {
    // Override in child classes if needed
  }

  /**
   * Validation dữ liệu cơ bản
   * @param {Object} data - Dữ liệu cần validate
   */
  static validate(data) {
    if (!data || typeof data !== 'object') {
      throw new Error('Data must be an object');
    }
    return true;
  }

  /**
   * Transform dữ liệu từ database
   * @param {Object} row - Dữ liệu từ DB
   */
  static transform(row) {
    if (!row) return null;

    // Có thể override trong class con để transform data
    return row;
  }
}
