/**
 * Base Model cho MySQL2 - Tương tự như BaseModel của MySQL nhưng cho raw queries
 */
export default class BaseModel {
  constructor() {
    this.tableName = this.constructor.tableName || '';
  }

  /**
   * Tên bảng - override trong class con
   */
  static get tableName() {
    throw new Error('tableName must be defined in child class');
  }

  /**
   * Các field cơ bản cho tất cả bảng
   */
  static getBaseFields() {
    return [
      'id',
      'created_at',
      'updated_at',
      'deleted_at'
    ];
  }

  /**
   * Các field của bảng - override trong class con
   */
  static getFields() {
    return this.getBaseFields();
  }

  /**
   * Build WHERE clause từ conditions object
   * @param {Object} conditions - <PERSON><PERSON><PERSON><PERSON> ki<PERSON> query
   * @param {string} operator - <PERSON>án tử kết nối (AND/OR)
   * @param {string} tableAlias - <PERSON><PERSON> củ<PERSON> bả<PERSON> (tùy chọn)
   */
  buildWhereClause(conditions, operator = 'AND', tableAlias = null) {
    if (!conditions || Object.keys(conditions).length === 0) {
      return { whereClause: '1', params: [] };
    }

    const clauses = [];
    const params = [];
    const prefix = tableAlias ? `${tableAlias}.` : '';

    for (const [key, value] of Object.entries(conditions)) {
      if (key === 'createTime' && typeof value === 'object') {
        // Xử lý range query cho createTime
        if (value.$gte !== undefined) {
          clauses.push(`${prefix}create_time >= ?`);
          params.push(value.$gte);
        }
        if (value.$lte !== undefined) {
          clauses.push(`${prefix}create_time <= ?`);
          params.push(value.$lte);
        }
        if (value.$gt !== undefined) {
          clauses.push(`${prefix}create_time > ?`);
          params.push(value.$gt);
        }
        if (value.$lt !== undefined) {
          clauses.push(`${prefix}create_time < ?`);
          params.push(value.$lt);
        }
      } else if (typeof value === 'object' && value !== null) {
        // Xử lý các operator đặc biệt
        if (value.$exists !== undefined) {
          if (value.$exists) {
            clauses.push(`${prefix}${key} IS NOT NULL`);
          } else {
            clauses.push(`${prefix}${key} IS NULL`);
          }
        }
        if (value.$nin !== undefined && Array.isArray(value.$nin)) {
          const placeholders = value.$nin.map(() => '?').join(', ');
          clauses.push(`${prefix}${key} NOT IN (${placeholders})`);
          params.push(...value.$nin);
        }
        if (value.$in !== undefined && Array.isArray(value.$in)) {
          const placeholders = value.$in.map(() => '?').join(', ');
          clauses.push(`${prefix}${key} IN (${placeholders})`);
          params.push(...value.$in);
        }
        if (value.$like !== undefined) {
          clauses.push(`${prefix}${key} LIKE ?`);
          params.push(value.$like);
        }
      } else if (value !== null && value !== undefined) {
        // Điều kiện đơn giản
        clauses.push(`${prefix}${key} = ?`);
        params.push(value);
      }
    }

    return {
      whereClause: clauses.length > 0 ? clauses.join(` ${operator} `) : '1',
      params
    };
  }

  /**
   * Build ORDER BY clause
   * @param {string|Object} orderBy - Chuỗi hoặc object định nghĩa sắp xếp
   */
  buildOrderByClause(orderBy) {
    if (!orderBy) return '';

    if (typeof orderBy === 'string') {
      return orderBy;
    }

    if (typeof orderBy === 'object') {
      const clauses = Object.entries(orderBy)
        .map(([field, direction]) => `${field} ${direction.toUpperCase()}`)
        .join(', ');
      return clauses;
    }

    return '';
  }

  /**
   * Build LIMIT clause
   * @param {number} limit - Số lượng record
   * @param {number} offset - Vị trí bắt đầu
   */
  buildLimitClause(limit, offset) {
    let clause = '';
    if (limit) {
      clause += ` LIMIT ${limit}`;
      if (offset) {
        clause += ` OFFSET ${offset}`;
      }
    }
    return clause;
  }

  /**
   * Validation dữ liệu cơ bản
   * @param {Object} data - Dữ liệu cần validate
   */
  validate(data) {
    if (!data || typeof data !== 'object') {
      throw new Error('Data must be an object');
    }
    return true;
  }

  /**
   * Chuẩn hóa dữ liệu trước khi lưu
   * @param {Object} data - Dữ liệu cần chuẩn hóa
   */
  sanitize(data) {
    const sanitized = { ...data };
    
    // Loại bỏ các field undefined
    Object.keys(sanitized).forEach(key => {
      if (sanitized[key] === undefined) {
        delete sanitized[key];
      }
    });

    return sanitized;
  }

  /**
   * Transform dữ liệu từ database
   * @param {Object} row - Dữ liệu từ DB
   */
  transform(row) {
    if (!row) return null;
    
    // Có thể override trong class con để transform data
    return row;
  }

  /**
   * Transform nhiều rows
   * @param {Array} rows - Mảng dữ liệu từ DB
   */
  transformMany(rows) {
    if (!Array.isArray(rows)) return [];
    return rows.map(row => this.transform(row));
  }
}
