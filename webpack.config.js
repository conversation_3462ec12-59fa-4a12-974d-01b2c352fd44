import path from 'path';
import { fileURLToPath } from 'url';
// eslint-disable-next-line node/no-unpublished-import
import webpack from 'webpack';
// eslint-disable-next-line node/no-unpublished-import
import nodeExternals from 'webpack-node-externals';

// ES modules don't have __dirname, so we need to create it
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default (env, argv) => {
  const isProduction = argv.mode === 'production';

  return {
    target: 'node', // Important for Node.js applications
    entry: './src/index.js',
    output: {
      path: path.resolve(__dirname, 'dist'),
      filename: 'bundle.js',
      clean: true, // Clean the output directory before emit
      library: {
        type: 'commonjs2',
      },
    },
    externals: [nodeExternals()], // Excludes node_modules from the bundle
    module: {
      rules: [
        {
          test: /\.js$/,
          exclude: /node_modules/,
          use: {
            loader: 'babel-loader',
            options: {
              presets: [
                [
                  '@babel/preset-env',
                  {
                    targets: {
                      node: '16',
                    },
                    modules: 'commonjs',
                  },
                ],
              ],
            },
          },
        },
      ],
    },
    resolve: {
      extensions: ['.js'],
      alias: {
        '@': path.resolve(__dirname, 'src'),
      },
    },
    plugins: [
      new webpack.DefinePlugin({
        'process.env.NODE_ENV': JSON.stringify(isProduction ? 'production' : 'development'),
      }),
      // Add banner to output file
      new webpack.BannerPlugin({
        banner: '#!/usr/bin/env node',
        raw: true,
      }),
      new webpack.IgnorePlugin({
        resourceRegExp: /^\.\/locale$/,
        contextRegExp: /moment$/,
      }),
    ],
    mode: isProduction ? 'production' : 'development',
    devtool: isProduction ? 'source-map' : 'eval-source-map',
    stats: 'errors-only', // Reduce output noise
  };
};
