# Chế độ môi trường (development, production)
NODE_ENV=development
# Cổng mà server sẽ lắng nghe
PORT=3000
# Chuỗi kết nối tới cơ sở dữ liệu MongoDB
MONGODB_URI=***************************************************
MONGOOSE_DEBUG=1
MONGOOSE_SERVER_SELECTION_TIMEOUT=30000
MONGOOSE_SOCKET_TIMEOUT=45000
# Database Mysql
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=Duy0509@
MYSQL_DATABASE=sys
MYSQL_CONNECTION_LIMIT=10
# Khóa bí mật để ký và xác thực "server to server"
SERVER_SECRET_KEY=1i6rKlzhi9t1yCI5Jt360qPgLaoUIC6KUbl0MK7V8l6bQfwegF8lmM1ODoEBX0H4
# Thời gian timeout cho Express (mili giây)
SERVER_TIMEOUT=60000