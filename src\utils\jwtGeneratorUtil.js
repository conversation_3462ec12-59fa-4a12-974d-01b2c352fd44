import 'dotenv/config';
import logger from '../config/logger.js';
import jwt from 'jsonwebtoken';

// The secret key you provided
const secretKey = process.env.JWT_SECRET_KEY;

// Payload for the JWT token
const payload = {
  userId: '1',
  username: 'admin',
  role: 'admin',
  // Add any other claims you need in your token
};

// Options for the token
const options = {
  // No expiresIn property means the token will never expire
  issuer: 'jx1-anaylyic-services',
};

// Generate the JWT token (unlimited lifetime)
const token = jwt.sign(payload, secretKey, options);

// Để tạo một JWT_SECRET mạnh, bạn có thể sử dụng Node.js để tạo một chuỗi ngẫu nhiên an toàn
// Run command: node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"

logger.info('JWT Token (never expires):');
logger.info(token);
