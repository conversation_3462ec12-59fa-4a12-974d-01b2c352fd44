import HTTPStatus from 'http-status';
import logger from '../config/logger.js';
import { errorResponse } from '../utils/apiResponseUltil.js';

/**
 * Middleware xác thực server-to-server bằng secret key
 * Kiểm tra header 'X-API-Key' hoặc 'Authorization'
 */
const serverAuthMiddleware = (req, res, next) => {
  try {
    // Lấy secret key từ environment variable
    const serverSecretKey = process.env.SERVER_SECRET_KEY || 'your-default-secret-key';

    // Lấy API key từ header (hỗ trợ cả 2 format)
    const apiKey =
      req.headers['x-api-key'] ||
      req.headers['authorization']?.replace('Bearer ', '') ||
      req.headers['authorization'];

    // Kiểm tra API key có tồn tại không
    if (!apiKey) {
      logger.warn('<PERSON>êu cầu thiếu API key:', {
        ip: req.ip,
        url: req.originalUrl,
        method: req.method,
      });
      return res
        .status(HTTPStatus.UNAUTHORIZED)
        .json(errorResponse('Thiếu API key. Sử dụng header X-API-Key hoặc Authorization.'));
    }

    // Xác thực API key
    if (apiKey !== serverSecretKey) {
      logger.warn('API key không hợp lệ:', {
        ip: req.ip,
        url: req.originalUrl,
        method: req.method,
        providedKey: apiKey.substring(0, 8) + '...', // Log một phần key để debug
      });
      return res.status(HTTPStatus.UNAUTHORIZED).json(errorResponse('API key không hợp lệ.'));
    }

    // Xác thực thành công
    logger.info('Server-to-server request authenticated:', {
      ip: req.ip,
      url: req.originalUrl,
      method: req.method,
    });

    next();
  } catch (error) {
    logger.error('Lỗi trong middleware xác thực:', error);
    return res.status(HTTPStatus.INTERNAL_SERVER_ERROR).json(errorResponse('Lỗi xác thực server.'));
  }
};

export default serverAuthMiddleware;
