/**
 * Error handler for api routes
 */

import <PERSON> from 'raven';
import Pretty<PERSON><PERSON><PERSON> from 'pretty-error';
import HTTPStatus from 'http-status';

import AppError, { RequiredError } from './errorService.js';
import { errorResponse } from '../utils/apiResponseUltil.js';
import logger from '../config/logger.js';

const isProd = process.env.NODE_ENV === 'production';
const isDev = process.env.NODE_ENV === 'development';

export default function logErrorService(err, req, res, next) {
  if (!err) {
    return new AppError('Error with the server!', HTTPStatus.INTERNAL_SERVER_ERROR, true);
  }

  if (isProd) {
    const raven = new Raven.Client(process.env.RAVEN_ID);
    raven.captureException(err);
  }

  if (isDev) {
    const pe = new PrettyError();
    pe.skipNodeFiles();
    pe.skipPackage('express');

    logger.debug(pe.render(err));
  }

  let errorData = null;

  if (err.errors) {
    errorData = {};
    const { errors } = err;
    if (Array.isArray(errors)) {
      errorData = RequiredError.makePretty(errors);
    } else {
      Object.keys(errors).forEach((key) => {
        // eslint-disable-next-line security/detect-object-injection
        errorData[key] = errors[key].message;
      });
    }
  }

  const statusCode = err.status || HTTPStatus.INTERNAL_SERVER_ERROR;

  res
    .status(statusCode)
    .json(errorResponse(err.message || 'Internal Server Error.', errorData, err.stack));

  return next();
}
