import dotenv from 'dotenv';
import MySQL2Service from './src/services/mysql2Service.js';
import User from './src/models/mysql2/userModel.js';
import moment from 'moment';

// Load environment variables
dotenv.config();

console.log('🚀 Starting MySQL2 Data Comparison Test...\n');

async function testMySQL2Connection() {
  console.log('📡 Testing MySQL2 Connection...');
  try {
    const isConnected = await MySQL2Service.testConnection();
    if (isConnected) {
      console.log('✅ MySQL2 connection successful!\n');
      return true;
    } else {
      console.log('❌ MySQL2 connection failed!\n');
      return false;
    }
  } catch (error) {
    console.log('❌ MySQL2 connection error:', error.message, '\n');
    return false;
  }
}

async function testBasicQueries() {
  console.log('📊 Testing Basic Queries...');
  
  try {
    // Test 1: Total accounts
    console.log('🔍 Test 1: Total Accounts');
    const totalAccounts = await User.estimatedDocumentCount();
    console.log(`   Result: ${totalAccounts} accounts\n`);

    // Test 2: Unique IPs
    console.log('🔍 Test 2: Unique IPs');
    const uniqueIPs = await User.distinct('ip');
    console.log(`   Result: ${uniqueIPs.length} unique IPs`);
    console.log(`   Sample IPs: ${uniqueIPs.slice(0, 5).join(', ')}\n`);

    // Test 3: Accounts registered today
    console.log('🔍 Test 3: Accounts Registered Today');
    const todayStart = moment().startOf('day').unix();
    const todayEnd = moment().endOf('day').unix();
    const todayAccounts = await User.countDocuments({
      createTime: { $gte: todayStart, $lte: todayEnd }
    });
    console.log(`   Result: ${todayAccounts} accounts registered today\n`);

    // Test 4: IPs registered today
    console.log('🔍 Test 4: IPs Registered Today');
    const todayIPs = await User.distinct('ip', {
      createTime: { $gte: todayStart, $lte: todayEnd },
      ip: { $exists: true, $nin: [null, ''] }
    });
    console.log(`   Result: ${todayIPs.length} unique IPs registered today\n`);

    // Test 5: Sample user data
    console.log('🔍 Test 5: Sample User Data');
    const sampleUsers = await MySQL2Service.query(
      'SELECT user_id, username, ip, FROM_UNIXTIME(create_time) as registration_time FROM user ORDER BY create_time DESC LIMIT 5'
    );
    console.log('   Recent registrations:');
    sampleUsers.forEach(user => {
      console.log(`   - User ${user.user_id}: ${user.username} | IP: ${user.ip} | Time: ${user.registration_time}`);
    });
    console.log('');

    return true;
  } catch (error) {
    console.log('❌ Query test failed:', error.message, '\n');
    return false;
  }
}

async function testDateRangeQueries() {
  console.log('📅 Testing Date Range Queries...');
  
  try {
    // Test last 7 days statistics
    console.log('🔍 Test: Last 7 Days Statistics');
    const last7Days = [];
    
    for (let i = 6; i >= 0; i--) {
      const date = moment().subtract(i, 'days');
      const dayStart = date.clone().startOf('day').unix();
      const dayEnd = date.clone().endOf('day').unix();
      
      const [accounts, ips] = await Promise.all([
        User.countDocuments({
          createTime: { $gte: dayStart, $lte: dayEnd }
        }),
        User.distinct('ip', {
          createTime: { $gte: dayStart, $lte: dayEnd },
          ip: { $exists: true, $nin: [null, ''] }
        })
      ]);
      
      const cloneCoeff = ips.length > 0 ? (accounts / ips.length).toFixed(3) : 0;
      
      last7Days.push({
        date: date.format('YYYY-MM-DD'),
        accounts,
        ips: ips.length,
        cloneCoefficient: cloneCoeff
      });
    }
    
    console.log('   Last 7 days data:');
    last7Days.forEach(day => {
      console.log(`   ${day.date}: ${day.accounts} accounts, ${day.ips} IPs, clone: ${day.cloneCoefficient}`);
    });
    console.log('');

    return true;
  } catch (error) {
    console.log('❌ Date range test failed:', error.message, '\n');
    return false;
  }
}

async function testOverviewAPIData() {
  console.log('🎯 Testing Overview API Data...');
  
  try {
    // Simulate getOverview() function
    const [totalAccounts, distinctIPs] = await Promise.all([
      User.estimatedDocumentCount(),
      User.distinct('ip')
    ]);

    const validIPs = distinctIPs.filter(ip => ip && ip !== '');
    
    const overviewData = {
      totalAccounts: Number(totalAccounts) || 0,
      totalUniqueIPs: validIPs.length,
    };
    
    console.log('📋 Overview API Data Simulation:');
    console.log(`   Total Accounts: ${overviewData.totalAccounts}`);
    console.log(`   Total Unique IPs: ${overviewData.totalUniqueIPs}`);
    console.log('');

    // Test growth calculation for today vs yesterday
    const today = moment();
    const yesterday = moment().subtract(1, 'day');
    
    const todayStart = today.clone().startOf('day').unix();
    const todayEnd = today.clone().endOf('day').unix();
    const yesterdayStart = yesterday.clone().startOf('day').unix();
    const yesterdayEnd = yesterday.clone().endOf('day').unix();
    
    const [todayAccounts, yesterdayAccounts] = await Promise.all([
      User.countDocuments({
        createTime: { $gte: todayStart, $lte: todayEnd }
      }),
      User.countDocuments({
        createTime: { $gte: yesterdayStart, $lte: yesterdayEnd }
      })
    ]);
    
    const growth = yesterdayAccounts === 0 ? 
      (todayAccounts > 0 ? 100 : 0) : 
      ((todayAccounts - yesterdayAccounts) / yesterdayAccounts) * 100;
    
    console.log('📈 Growth Calculation Example (Today vs Yesterday):');
    console.log(`   Today: ${todayAccounts} accounts`);
    console.log(`   Yesterday: ${yesterdayAccounts} accounts`);
    console.log(`   Growth: ${growth.toFixed(2)}%`);
    console.log('');

    return true;
  } catch (error) {
    console.log('❌ Overview API test failed:', error.message, '\n');
    return false;
  }
}

async function runAllTests() {
  const connectionOk = await testMySQL2Connection();
  if (!connectionOk) {
    console.log('🚫 Cannot proceed with tests due to connection issues.');
    return;
  }

  const basicOk = await testBasicQueries();
  const dateRangeOk = await testDateRangeQueries();
  const overviewOk = await testOverviewAPIData();

  console.log('📝 Test Summary:');
  console.log(`   MySQL2 Connection: ${connectionOk ? '✅' : '❌'}`);
  console.log(`   Basic Queries: ${basicOk ? '✅' : '❌'}`);
  console.log(`   Date Range Queries: ${dateRangeOk ? '✅' : '❌'}`);
  console.log(`   Overview API Data: ${overviewOk ? '✅' : '❌'}`);
  console.log('');

  if (connectionOk && basicOk && dateRangeOk && overviewOk) {
    console.log('🎉 All tests passed! MySQL2 replacement is working correctly.');
    console.log('📄 Check mysql2-comparison-queries.sql for direct database queries.');
  } else {
    console.log('⚠️  Some tests failed. Please check the configuration and database.');
  }
}

// Run tests
runAllTests().catch(error => {
  console.error('💥 Test execution failed:', error);
  process.exit(1);
});
