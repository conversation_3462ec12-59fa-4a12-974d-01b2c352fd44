# Tái cấu trúc MySQL2 Models - <PERSON><PERSON><PERSON> nhất với MySQL Pattern

## Tổng quan

Đã tái cấu trúc thư mục `src/models/mysql2/` để đồng nhất với pattern của `src/models/mysql/`, loại bỏ việc viết raw SQL queries trực tiếp trong models và tách biệt responsibilities rõ ràng.

## Cấu trúc mới

### 1. BaseModel.js
- Class cơ sở cho tất cả MySQL2 models
- Cung cấp các method tiện ích: `buildWhereClause`, `buildOrderByClause`, `buildLimitClause`
- Hỗ trợ validation và data transformation
- Tương tự như `mysql/BaseModel.js` nhưng cho raw SQL queries

### 2. helpers.js
- **Enums**: Các constant values (USER_STATUS, PROFILE_STATUS, SIGNUP_SOURCE, GENDER, etc.)
- **Fields**: <PERSON><PERSON><PERSON> ngh<PERSON> c<PERSON>c field chung (id, userId, email, phone, status, etc.)  
- **Queries**: <PERSON><PERSON><PERSON> query pattern chung (byUserId, byStatus, byTimeRange, today, thisMonth, etc.)
- **Utils**: Utility functions (formatIP, validateEmail, sanitizeString, etc.)

### 3. Models

#### userModel.js
```javascript
// Extends BaseModel, sử dụng helpers
class UserModel extends BaseModel {
  static get tableName() { return 'user'; }
  
  // Business logic methods sử dụng Queries pattern
  async findActive(options = {}) {
    return await this.find(Queries.activeUsers().where, options);
  }
  
  async countToday() {
    return await this.countDocuments(Queries.today().where);
  }
}
```

#### profileModel.js
```javascript
// Tương tự, với validation và transformation
class ProfileModel extends BaseModel {
  calculateAge(birthDate) { /* logic */ }
  
  async findComplete(options = {}) {
    return await this.find(
      Queries.byStatus(Enums.PROFILE_STATUS.COMPLETE).where, 
      options
    );
  }
}
```

#### signUpSourceModel.js
```javascript
// Pattern thống kê và analytics
class SignUpSourceModel extends BaseModel {
  async getSourceStats() {
    // Raw SQL for complex aggregation
  }
  
  async findFromWebsite(options = {}) {
    return await this.findBySource(Enums.SIGNUP_SOURCE.WEBSITE, options);
  }
}
```

### 4. index.js
Export tất cả models và helpers để import dễ dàng:
```javascript
import { UserModel, Enums, Queries } from '../models/mysql2/index.js';
```

## So sánh với MySQL (Sequelize)

| Aspect | MySQL (Sequelize) | MySQL2 (Raw SQL) |
|--------|------------------|-------------------|
| **Base Class** | Extends Sequelize Model | Extends custom BaseModel |
| **Query Building** | Sequelize Query Builder | buildWhereClause + raw SQL |
| **Field Definition** | DataTypes + validation | Field helpers + manual validation |
| **Relationships** | associate() method | Manual JOIN queries |
| **Business Logic** | Static methods using Sequelize | Instance methods using MySQL2Service |

## Pattern Usage Examples

### 1. Basic CRUD
```javascript
// Tìm user active
const activeUsers = await UserModel.findActive({ limit: 10 });

// Đếm user hôm nay  
const todayCount = await UserModel.countToday();

// Tìm theo điều kiện phức tạp
const users = await UserModel.find({
  createTime: { $gte: startTime, $lte: endTime },
  ip: { $exists: true }
}, { orderBy: 'created_at DESC', limit: 100 });
```

### 2. Using Helpers
```javascript
// Sử dụng Queries helpers
const recentUsers = await UserModel.find(
  Queries.recentUsers(30).where,
  Queries.withPagination(1, 20)
);

// Sử dụng Enums
const completeProfiles = await ProfileModel.find(
  Queries.byStatus(Enums.PROFILE_STATUS.COMPLETE).where
);
```

### 3. Complex Analytics
```javascript
// Thống kê signup source
const sourceStats = await SignUpSourceModel.getSourceStats();

// Aggregate với raw SQL
const stats = await SignUpSourceModel.getSignUpSourceStats(startTime, endTime);
```

## Import Pattern

### Trước (không đồng nhất)
```javascript
import User from '../models/mysql2/userModel.js';
// Dynamic import trong method
const MySQL2Service = (await import('../../services/mysql2Service.js')).default;
```

### Sau (đồng nhất với MySQL)
```javascript
import { UserModel, SignUpSourceModel, Queries, Enums } from '../models/mysql2/index.js';
import MySQL2Service from './mysql2Service.js';
```

## Benefits

1. **Consistency**: Cùng pattern với MySQL models
2. **Maintainability**: Tách biệt queries khỏi business logic
3. **Reusability**: Helpers có thể tái sử dụng
4. **Type Safety**: Enum values và field definitions rõ ràng
5. **Testability**: Dễ test và mock

## Migration Notes

- Tất cả existing code vẫn hoạt động do giữ nguyên public interface
- Services đã được update để sử dụng import trực tiếp thay vì dynamic import
- Controllers không cần thay đổi gì
- Có thể dần dần migrate sang sử dụng helpers pattern mới
