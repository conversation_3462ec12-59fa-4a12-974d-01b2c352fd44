import 'dotenv/config'; // Ensure dotenv is loaded before checking env vars
import mongoose from 'mongoose';
import logger from './logger.js';
import { ConfigurationError, DatabaseConnectionError } from '../services/errorService.js';

// Remove the warning with Promise
mongoose.Promise = global.Promise;

// If debug run the mongoose debug options
if (process.env.MONGOOSE_DEBUG == '1') {
  mongoose.set('debug', function (collectionName, method, query, doc) {
    logger.debug(`[Mongoose] ${collectionName}.${method}`, JSON.stringify(query), doc);
  });
}

// MongoDB connection configuration
const MONGODB_URI = process.env.MONGODB_URI;
if (!MONGODB_URI) {
  logger.error('Lỗi: Biến môi trường MONGODB_URI chưa được đặt.');
  throw new ConfigurationError('MONGODB_URI environment variable is not set');
}

const mongodbOptions = {
  serverSelectionTimeoutMS: process.env.MONGOOSE_SERVER_SELECTION_TIMEOUT || 30000,
  socketTimeoutMS: process.env.MONGOOSE_SOCKET_TIMEOUT || 45000,
  maxPoolSize: process.env.MONGODB_MAX_POOL_SIZE || 10,
  minPoolSize: process.env.MONGODB_MIN_POOL_SIZE || 1,
  maxIdleTimeMS: process.env.MONGODB_MAX_IDLE_TIME || 30000,
  retryWrites: true,
  w: 'majority',
};

// Main MongoDB connection
let mongodbConnection;

try {
  mongodbConnection = mongoose.createConnection(
    `${MONGODB_URI}?authMechanism=SCRAM-SHA-256&directConnection=true`,
    mongodbOptions
  );

  mongodbConnection.on('connected', () => {
    logger.info('Đã kết nối tới MongoDB chính');
  });

  mongodbConnection.on('error', (err) => {
    logger.error('Lỗi kết nối MongoDB chính:', err);
  });

  mongodbConnection.on('disconnected', () => {
    logger.warn('MongoDB chính đã ngắt kết nối');
  });
} catch (err) {
  logger.error('Lỗi tạo MongoDB connection:', err);
  throw new DatabaseConnectionError(`MongoDB connection error: ${err.message}`, {
    originalError: err,
  });
}

// Sabo ID MongoDB connection (separate database)
let saboIdConnection;

try {
  saboIdConnection = mongoose.createConnection(
    `${process.env.MONGODB_URI}/sabo-id?authMechanism=SCRAM-SHA-256&directConnection=true`,
    mongodbOptions
  );

  saboIdConnection.on('connected', () => {
    logger.info('Đã kết nối tới MongoDB Sabo ID');
  });

  saboIdConnection.on('error', (err) => {
    logger.error('Lỗi kết nối MongoDB Sabo ID:', err);
  });

  saboIdConnection.on('disconnected', () => {
    logger.warn('MongoDB Sabo ID đã ngắt kết nối');
  });
} catch (err) {
  logger.error('Lỗi tạo Sabo ID MongoDB connection:', err);
  throw new DatabaseConnectionError(`Sabo ID MongoDB connection error: ${err.message}`, {
    originalError: err,
  });
}

// Handle graceful shutdown
const shutdownHandler = async () => {
  try {
    if (mongodbConnection) {
      await mongodbConnection.close();
      logger.info('✅ Đã đóng kết nối MongoDB chính');
    }
  } catch (error) {
    logger.error('❌ Lỗi khi đóng kết nối MongoDB chính:', error);
    throw error;
  }

  try {
    if (saboIdConnection) {
      await saboIdConnection.close();
      logger.info('✅ Đã đóng kết nối MongoDB Sabo ID');
    }
  } catch (error) {
    logger.error('❌ Lỗi khi đóng kết nối MongoDB Sabo ID:', error);
    throw error;
  }
};

// Register process signal handlers
process.on('SIGINT', shutdownHandler);
process.on('SIGTERM', shutdownHandler);

export { mongodbConnection, saboIdConnection };
