import mysql from 'mysql2/promise';
import logger from './logger.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Check required environment variables for MySQL2
if (
  !process.env.MYSQL2_USER ||
  process.env.MYSQL2_PASSWORD === undefined ||
  !process.env.MYSQL2_DATABASE
) {
  logger.error('❌ Lỗi: Thiếu biến môi trường MySQL2 (MYSQL2_USER, MYSQL2_PASSWORD, MYSQL2_DATABASE)');
  throw new Error('Missing required MySQL2 environment variables');
}

// MySQL2 connection configuration (Sabo ID database)
const mysql2Config = {
  host: process.env.MYSQL2_HOST || 'localhost',
  port: Number(process.env.MYSQL2_PORT) || 3306,
  user: process.env.MYSQL2_USER,
  password: process.env.MYSQL2_PASSWORD,
  database: process.env.MYSQL2_DATABASE,
  charset: 'utf8mb4',
  timezone: '+07:00',
  connectionLimit: Number(process.env.MYSQL2_CONNECTION_LIMIT) || 10,
  multipleStatements: true,
  dateStrings: true,
  supportBigNumbers: true,
  bigNumberStrings: true,
  // Pool configuration
  acquireTimeout: Number(process.env.MYSQL2_ACQUIRE_TIMEOUT) || 60000,
  waitForConnections: true,
  queueLimit: 0,
  enableKeepAlive: true,
  keepAliveInitialDelay: 0,
};

// Create MySQL2 connection pool
let mysql2Pool;

try {
  mysql2Pool = mysql.createPool(mysql2Config);
  logger.info('🔧 Đã tạo MySQL2 (Sabo ID) connection pool');
} catch (err) {
  logger.error('❌ Lỗi tạo MySQL2 connection pool:', err);
  throw err;
}

// Test the connection immediately
(async () => {
  try {
    const connection = await mysql2Pool.getConnection();
    logger.info('✅ Đã kết nối tới MySQL2 (Sabo ID) database thành công');
    connection.release();
  } catch (err) {
    logger.error('❌ Lỗi kết nối MySQL2:', err);
    // Không throw error để app vẫn có thể chạy
  }
})();

// Handle graceful shutdown
const shutdownHandler = async () => {
  try {
    if (mysql2Pool) {
      await mysql2Pool.end();
      logger.info('✅ Đã đóng kết nối MySQL2 (Sabo ID)');
    }
  } catch (error) {
    logger.error('❌ Lỗi khi đóng kết nối MySQL2:', error);
    throw error;
  }
};

// Register process signal handlers
process.on('SIGINT', shutdownHandler);
process.on('SIGTERM', shutdownHandler);

export { mysql2Pool };
