import BaseModel from './BaseModel.js';
import { Enums, Fields, Queries, Utils } from './helpers.js';
import MySQL2Service from '../../services/mysql2Service.js';
import logger from '../../config/logger.js';

/**
 * Model SignUpSource - Quản lý nguồn đăng ký người dùng
 */
class SignUpSourceModel extends BaseModel {
  static get tableName() {
    return 'sign_up_source';
  }

  constructor() {
    super();
    this.tableName = SignUpSourceModel.tableName;
  }

  /**
   * <PERSON><PERSON><PERSON> ngh<PERSON>a các field của bảng sign_up_source
   */
  static getFields() {
    return [
      ...super.getBaseFields(),
      'user_id',
      'source',
      'source_detail',
      'referrer_url',
      'utm_source',
      'utm_medium',
      'utm_campaign',
      'utm_content',
      'utm_term',
      'create_time'
    ];
  }

  /**
   * Lấy enum source
   */
  static getSourceEnum() {
    return Enums.SIGNUP_SOURCE;
  }

  /**
   * Transform dữ liệu sign up source
   */
  transform(row) {
    if (!row) return null;
    
    return {
      ...row,
      source_detail: Utils.sanitizeString(row.source_detail),
      referrer_url: Utils.sanitizeString(row.referrer_url),
      utm_source: Utils.sanitizeString(row.utm_source),
      utm_medium: Utils.sanitizeString(row.utm_medium),
      utm_campaign: Utils.sanitizeString(row.utm_campaign),
      utm_content: Utils.sanitizeString(row.utm_content),
      utm_term: Utils.sanitizeString(row.utm_term),
      source_name: this.getSourceName(row.source),
      createTimeFormatted: row.create_time ? Utils.timestampToDate(row.create_time) : null,
    };
  }

  /**
   * Lấy tên source từ enum
   */
  getSourceName(sourceValue) {
    const sourceEnum = this.constructor.getSourceEnum();
    return Object.keys(sourceEnum).find(key => sourceEnum[key] === sourceValue) || 'UNKNOWN';
  }

  /**
   * Đếm tổng số sign up source
   */
  async estimatedDocumentCount() {
    try {
      return await this.countDocuments();
    } catch (error) {
      logger.error('SignUpSourceModel.estimatedDocumentCount error:', error);
      throw error;
    }
  }

  /**
   * Đếm số sign up source theo điều kiện
   * @param {Object} conditions - Điều kiện query từ helpers
   */
  async countDocuments(conditions = {}) {
    try {
      const { whereClause, params } = this.buildWhereClause(conditions);
      return await MySQL2Service.count(this.tableName, whereClause, params);
    } catch (error) {
      logger.error('SignUpSourceModel.countDocuments error:', error);
      throw error;
    }
  }

  /**
   * Thống kê theo nguồn đăng ký (giữ lại method cũ để tương thích)
   * @param {number} startTime - Thời gian bắt đầu (timestamp)
   * @param {number} endTime - Thời gian kết thúc (timestamp)
   */
  async getSignUpSourceStats(startTime, endTime) {
    try {
      const sql = `
        SELECT 
          source,
          COUNT(*) as count,
          COUNT(DISTINCT user_id) as unique_users
        FROM ${this.tableName} 
        WHERE create_time BETWEEN ? AND ?
        GROUP BY source
        ORDER BY count DESC
      `;
      const rows = await MySQL2Service.query(sql, [startTime, endTime]);
      
      return rows.map(row => ({
        ...row,
        source_name: this.getSourceName(row.source),
        count: Number(row.count),
        unique_users: Number(row.unique_users)
      }));
    } catch (error) {
      logger.error('SignUpSourceModel.getSignUpSourceStats error:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách sign up source theo điều kiện
   */
  async find(conditions = {}, options = {}) {
    try {
      const { whereClause, params } = this.buildWhereClause(conditions);
      let sql = `SELECT * FROM ${this.tableName}`;
      
      if (whereClause !== '1') {
        sql += ` WHERE ${whereClause}`;
      }

      const orderByClause = this.buildOrderByClause(options.orderBy);
      if (orderByClause) {
        sql += ` ORDER BY ${orderByClause}`;
      }

      const limitClause = this.buildLimitClause(options.limit, options.offset);
      if (limitClause) {
        sql += limitClause;
      }

      const rows = await MySQL2Service.query(sql, params);
      return this.transformMany(rows);
    } catch (error) {
      logger.error('SignUpSourceModel.find error:', error);
      throw error;
    }
  }

  // === QUERY HELPERS USING PATTERN ===

  /**
   * Tìm theo source
   */
  async findBySource(source, options = {}) {
    return await this.find({ source }, options);
  }

  /**
   * Tìm từ website
   */
  async findFromWebsite(options = {}) {
    return await this.findBySource(Enums.SIGNUP_SOURCE.WEBSITE, options);
  }

  /**
   * Tìm từ mobile app
   */
  async findFromMobileApp(options = {}) {
    return await this.findBySource(Enums.SIGNUP_SOURCE.MOBILE_APP, options);
  }

  /**
   * Tìm từ social media
   */
  async findFromSocialMedia(options = {}) {
    return await this.findBySource(Enums.SIGNUP_SOURCE.SOCIAL_MEDIA, options);
  }

  /**
   * Đếm theo source
   */
  async countBySource(source) {
    return await this.countDocuments({ source });
  }

  /**
   * Thống kê theo source (pattern mới)
   */
  async getSourceStats() {
    try {
      const sql = `
        SELECT 
          source,
          COUNT(*) as count,
          COUNT(*) * 100.0 / (SELECT COUNT(*) FROM ${this.tableName} WHERE deleted_at IS NULL) as percentage
        FROM ${this.tableName} 
        WHERE deleted_at IS NULL
        GROUP BY source
        ORDER BY count DESC
      `;
      
      const rows = await MySQL2Service.query(sql);
      
      return rows.map(row => ({
        source: row.source,
        sourceName: this.getSourceName(row.source),
        count: Number(row.count),
        percentage: Number(row.percentage).toFixed(2)
      }));
    } catch (error) {
      logger.error('SignUpSourceModel.getSourceStats error:', error);
      throw error;
    }
  }
}

// Export instance của class
export default new SignUpSourceModel();
