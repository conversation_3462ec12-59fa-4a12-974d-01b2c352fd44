-- ===================================================================
-- MYSQL QUERIES TO COMPARE DATA BETWEEN DATABASE AND API RESPONSES
-- Database: MySQL2 (Sabo ID replacement) - Host: *************:5307
-- ===================================================================

-- 1. OVERVIEW STATISTICS QUERIES
-- ===================================================================

-- 1.1 Total Accounts (tương đương User.estimatedDocumentCount())
SELECT COUNT(*) as total_accounts FROM user;

-- 1.2 Total Unique IPs (tương đương User.distinct('ip'))
SELECT COUNT(DISTINCT ip) as total_unique_ips 
FROM user 
WHERE ip IS NOT NULL AND ip != '';

-- 1.3 List of unique IPs (để kiểm tra chi tiết)
SELECT DISTINCT ip 
FROM user 
WHERE ip IS NOT NULL AND ip != ''
ORDER BY ip;

-- 1.4 Sample of user data (để kiểm tra cấu trúc)
SELECT user_id, username, ip, create_time, status, type, role
FROM user 
LIMIT 10;

-- ===================================================================
-- 2. DAILY STATISTICS QUERIES  
-- ===================================================================

-- 2.1 Account registrations by date (for last 7 days)
SELECT 
  DATE(FROM_UNIXTIME(create_time)) as date,
  COUNT(*) as registered_accounts
FROM user 
WHERE create_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 7 DAY))
GROUP BY DATE(FROM_UNIXTIME(create_time))
ORDER BY date DESC;

-- 2.2 Unique IPs by date (for last 7 days)
SELECT 
  DATE(FROM_UNIXTIME(create_time)) as date,
  COUNT(DISTINCT ip) as registered_ips
FROM user 
WHERE create_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 7 DAY))
  AND ip IS NOT NULL AND ip != ''
GROUP BY DATE(FROM_UNIXTIME(create_time))
ORDER BY date DESC;

-- 2.3 Combined daily stats with clone coefficient
SELECT 
  DATE(FROM_UNIXTIME(create_time)) as date,
  COUNT(*) as registered_accounts,
  COUNT(DISTINCT ip) as registered_ips,
  ROUND(COUNT(*) / COUNT(DISTINCT ip), 3) as clone_coefficient
FROM user 
WHERE create_time >= UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 7 DAY))
  AND ip IS NOT NULL AND ip != ''
GROUP BY DATE(FROM_UNIXTIME(create_time))
ORDER BY date DESC;

-- ===================================================================
-- 3. DATE RANGE STATISTICS QUERIES
-- ===================================================================

-- 3.1 Accounts in specific date range (example: today)
SELECT COUNT(*) as accounts_today
FROM user 
WHERE create_time >= UNIX_TIMESTAMP(CURDATE()) 
  AND create_time < UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 1 DAY));

-- 3.2 IPs in specific date range (example: today)
SELECT COUNT(DISTINCT ip) as ips_today
FROM user 
WHERE create_time >= UNIX_TIMESTAMP(CURDATE()) 
  AND create_time < UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 1 DAY))
  AND ip IS NOT NULL AND ip != '';

-- 3.3 Accounts this week
SELECT COUNT(*) as accounts_this_week
FROM user 
WHERE create_time >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY));

-- 3.4 Accounts this month
SELECT COUNT(*) as accounts_this_month
FROM user 
WHERE create_time >= UNIX_TIMESTAMP(DATE_FORMAT(CURDATE(), '%Y-%m-01'));

-- ===================================================================
-- 4. TIME COMPARISON QUERIES (for growth calculation)
-- ===================================================================

-- 4.1 Today vs Yesterday accounts
SELECT 
  'today' as period,
  COUNT(*) as accounts
FROM user 
WHERE create_time >= UNIX_TIMESTAMP(CURDATE()) 
  AND create_time < UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 1 DAY))
UNION ALL
SELECT 
  'yesterday' as period,
  COUNT(*) as accounts
FROM user 
WHERE create_time >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL 1 DAY))
  AND create_time < UNIX_TIMESTAMP(CURDATE());

-- 4.2 This week vs Last week accounts
SELECT 
  'this_week' as period,
  COUNT(*) as accounts
FROM user 
WHERE create_time >= UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY))
UNION ALL
SELECT 
  'last_week' as period,
  COUNT(*) as accounts
FROM user 
WHERE create_time >= UNIX_TIMESTAMP(DATE_SUB(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 7 DAY))
  AND create_time < UNIX_TIMESTAMP(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY));

-- ===================================================================
-- 5. DATA VALIDATION QUERIES
-- ===================================================================

-- 5.1 Check data distribution by hour (for today)
SELECT 
  HOUR(FROM_UNIXTIME(create_time)) as hour,
  COUNT(*) as registrations
FROM user 
WHERE create_time >= UNIX_TIMESTAMP(CURDATE()) 
  AND create_time < UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 1 DAY))
GROUP BY HOUR(FROM_UNIXTIME(create_time))
ORDER BY hour;

-- 5.2 Check for duplicate IPs
SELECT 
  ip,
  COUNT(*) as account_count
FROM user 
WHERE ip IS NOT NULL AND ip != ''
GROUP BY ip
HAVING COUNT(*) > 1
ORDER BY account_count DESC
LIMIT 20;

-- 5.3 Check user status distribution
SELECT 
  status,
  COUNT(*) as count,
  ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM user), 2) as percentage
FROM user 
GROUP BY status
ORDER BY count DESC;

-- ===================================================================
-- 6. API ENDPOINT TEST QUERIES
-- ===================================================================

-- 6.1 Query for /api/overview endpoint
-- This should match the getOverview() function results
SELECT 
  (SELECT COUNT(*) FROM user) as totalAccounts,
  (SELECT COUNT(DISTINCT ip) FROM user WHERE ip IS NOT NULL AND ip != '') as totalUniqueIPs;

-- 6.2 Query for /api/overview/by-date endpoint (example: today, type=1 accounts)
-- This should match getOverviewByDate() with type=1
SELECT COUNT(*) as total
FROM user 
WHERE create_time >= UNIX_TIMESTAMP(CURDATE()) 
  AND create_time < UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 1 DAY));

-- 6.3 Query for /api/overview/by-date endpoint (example: today, type=2 IPs)
-- This should match getOverviewByDate() with type=2
SELECT COUNT(DISTINCT ip) as total
FROM user 
WHERE create_time >= UNIX_TIMESTAMP(CURDATE()) 
  AND create_time < UNIX_TIMESTAMP(DATE_ADD(CURDATE(), INTERVAL 1 DAY))
  AND ip IS NOT NULL AND ip != '';

-- ===================================================================
-- 7. DEBUGGING QUERIES
-- ===================================================================

-- 7.1 Check table structure
DESCRIBE user;

-- 7.2 Check recent registrations
SELECT user_id, username, ip, FROM_UNIXTIME(create_time) as registration_time
FROM user 
ORDER BY create_time DESC 
LIMIT 10;

-- 7.3 Check timestamp range in database
SELECT 
  MIN(create_time) as min_timestamp,
  MAX(create_time) as max_timestamp,
  FROM_UNIXTIME(MIN(create_time)) as min_date,
  FROM_UNIXTIME(MAX(create_time)) as max_date
FROM user;

-- ===================================================================
-- 8. PERFORMANCE CHECK QUERIES
-- ===================================================================

-- 8.1 Check indexes on user table
SHOW INDEXES FROM user;

-- 8.2 Explain query performance for common operations
EXPLAIN SELECT COUNT(*) FROM user WHERE create_time >= UNIX_TIMESTAMP(CURDATE());
EXPLAIN SELECT COUNT(DISTINCT ip) FROM user WHERE ip IS NOT NULL;

-- ===================================================================
-- HOW TO USE THESE QUERIES:
-- ===================================================================
-- 1. Connect to MySQL2 database: mysql -h ************* -P 5307 -u jx-bq-slave -p sabo_id
-- 2. Run queries individually to check data
-- 3. Compare results with API responses
-- 4. Use timestamp conversion: FROM_UNIXTIME(create_time) to convert Unix timestamp to readable date
-- 5. Use UNIX_TIMESTAMP(date_string) to convert date string to Unix timestamp
