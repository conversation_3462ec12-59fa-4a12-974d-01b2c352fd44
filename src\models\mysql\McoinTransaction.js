import BaseModel from './BaseModel.js';
import { CommonEnums, CommonFields, CommonScopes } from './ModelUtils.js';

/**
 * McoinTransaction Model
 * Represents M-coin transactions in the system
 */
export default class McoinTransaction extends BaseModel {
  static get tableName() {
    return 'mcoin_transactions';
  }

  static getAttributes(DataTypes) {
    return Object.assign({}, super.getAttributes(DataTypes), {
      // Transaction information
      transactionId: {
        type: DataTypes.STRING(255),
        allowNull: false,
        field: 'transaction_id',
        comment: 'Transaction ID',
      },

      // Game information
      gameId: {
        type: DataTypes.BIGINT,
        allowNull: false,
        field: 'game_id',
        comment: 'ID game',
      },

      // Account information
      accountId: CommonFields.accountId(DataTypes),
      accountName: CommonFields.accountName(DataTypes),

      // Order information
      orderId: {
        type: DataTypes.STRING(255),
        allowNull: false,
        field: 'order_id',
        comment: 'Order ID',
      },

      // Amount information
      amount: CommonFields.amount(DataTypes),
      coinAmount: CommonFields.coinAmount(DataTypes),

      // Payment method
      paymentMethod: CommonFields.paymentMethod(DataTypes),

      // Status
      status: CommonFields.status(DataTypes),

      // Additional data
      metaData: CommonFields.metaData(DataTypes),
    });
  }

  static getOptions() {
    return Object.assign({}, super.getOptions(), {
      indexes: [
        {
          fields: ['transaction_id'],
        },
        {
          fields: ['game_id'],
        },
        {
          fields: ['account_id'],
        },
        {
          fields: ['order_id'],
          unique: true,
        },
        {
          fields: ['status'],
        },
        {
          fields: ['payment_method'],
        },
        {
          fields: ['created_at'],
        },
        {
          fields: ['account_id', 'game_id'],
        },
        {
          fields: ['account_id', 'status'],
        },
        {
          fields: ['game_id', 'status'],
        },
      ],
    });
  }

  /**
   * Get transaction status enum
   * @returns {Object} Status enum
   */
  static getStatusEnum() {
    return CommonEnums.STATUS;
  }

  /**
   * Get payment method enum
   * @returns {Object} Payment method enum
   */
  static getPaymentMethodEnum() {
    return CommonEnums.PAYMENT_METHOD;
  }

  // Scope methods using CommonScopes
  static scopePending() {
    return CommonScopes.byStatus(CommonEnums.STATUS.PENDING);
  }

  static scopeCompleted() {
    return CommonScopes.byStatus(CommonEnums.STATUS.COMPLETED);
  }

  static scopeFailed() {
    return CommonScopes.byStatus(CommonEnums.STATUS.FAILED);
  }

  static scopeCancelled() {
    return CommonScopes.byStatus(CommonEnums.STATUS.CANCELLED);
  }

  static scopeByAccount(accountId) {
    return CommonScopes.byAccount(accountId);
  }

  static scopeByGame(gameId) {
    return { where: { gameId } };
  }

  static scopeByPaymentMethod(paymentMethod) {
    return CommonScopes.byStatus(paymentMethod);
  }

  static scopeByOrder(orderId) {
    return { where: { orderId } };
  }

  static scopeByAmountRange(minAmount, maxAmount) {
    return CommonScopes.byAmountRange(minAmount, maxAmount);
  }

  static scopeToday() {
    return CommonScopes.today();
  }

  static scopeThisMonth() {
    return CommonScopes.thisMonth();
  }
}
